# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=your_database_name
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_CHARSET=utf8mb4

# Application Settings
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Email Configuration
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Your Application Name
EMAIL_REPLY_TO=<EMAIL>

# SMTP Configuration (Optional - for development)
SMTP_HOST=your.smtp.host
SMTP_PORT=465
SMTP_USERNAME=your_smtp_username
SMTP_PASSWORD=your_smtp_password
SMTP_ENCRYPTION=ssl

# Security Settings
SESSION_LIFETIME=3600
CSRF_TOKEN_LIFETIME=3600
PASSWORD_RESET_TOKEN_LIFETIME=3600
MAX_RESET_ATTEMPTS=3

# System Settings
SYSTEM_MAINTENANCE=false
LOG_LEVEL=info
