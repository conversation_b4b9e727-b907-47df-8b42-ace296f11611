# Forgot Password System Documentation

## Overview

A secure password reset system has been implemented for the Calaguas Booking System. This system allows users to reset their passwords via email verification with comprehensive security measures.

## Files Created

### Core Files
- **`forgotpassword.php`** - Main forgot password form where users enter their email
- **`inc.forgotpassword.php`** - Backend processing for password reset requests
- **`resetpassword.php`** - Password reset form accessed via email link
- **`inc.resetpassword.php`** - Backend processing for new password submission

### Configuration Files
- **`config/email.php`** - Email configuration and template functions
- **`create_password_reset_tables.php`** - Database setup and verification script

### Modified Files
- **`login.php`** - Updated forgot password link to point to `forgotpassword.php`

## Database Requirements

The system uses existing database tables:

### `operator_account` table
Required fields:
- `id` - User identification
- `username` - Login username
- `password` - Hashed password (updated during reset)
- `email` - Email address for reset notifications
- `rememberToken` - Stores hashed reset token
- `accountExpired` - Stores token expiry timestamp
- `accountStatus` - Must be 'Activated' for reset to work

### `system_logs` table
Used for logging password reset activities:
- `id` - Auto-increment primary key
- `type` - Log type (e.g., 'Password Reset', 'Email')
- `description` - Detailed log description
- `created_at` - Timestamp

## Security Features

### 1. Rate Limiting
- Maximum 3 password reset attempts per hour per session
- Prevents brute force attacks

### 2. Token Security
- Cryptographically secure random tokens (64 characters)
- Tokens are hashed before database storage
- 1-hour expiry time for all reset tokens

### 3. CSRF Protection
- All forms include CSRF tokens
- Tokens expire after 1 hour

### 4. Password Validation
- Minimum 8 characters
- Must contain uppercase, lowercase, and number
- Passwords are hashed using PHP's `password_hash()` with BCRYPT

### 5. Email Security
- Generic success messages (don't reveal if email exists)
- HTML email templates with security notices
- Comprehensive logging of all email attempts

### 6. Session Security
- Secure session configuration
- Session regeneration on successful operations

## User Flow

1. **Request Reset**
   - User visits `forgotpassword.php`
   - Enters email address
   - System generates secure token and sends email

2. **Email Verification**
   - User receives email with reset link
   - Link contains unique token parameter
   - Link expires after 1 hour

3. **Password Reset**
   - User clicks link to access `resetpassword.php`
   - System validates token and expiry
   - User enters new password (with confirmation)

4. **Completion**
   - Password is updated in database
   - Reset token is cleared
   - Confirmation email is sent
   - User is redirected to login page

## Installation & Setup

### 1. Run Database Setup
```bash
# Visit in browser to create/verify tables
http://yourdomain.com/create_password_reset_tables.php
```

### 2. Configure Email (Optional)
Edit `config/email.php` to customize:
- Email addresses and names
- Email template styling
- SMTP settings (if needed)

### 3. Test the System
```bash
# Test forgot password form
http://yourdomain.com/forgotpassword.php

# Test with existing user email
# Check email delivery and reset process
```

### 4. Security Cleanup
After testing, delete the setup file:
```bash
rm create_password_reset_tables.php
```

## Email Configuration

### Basic Configuration
The system uses PHP's built-in `mail()` function by default. For production environments, consider:

1. **SMTP Configuration** - Use PHPMailer or similar for reliable delivery
2. **Email Authentication** - Configure SPF, DKIM, and DMARC records
3. **Delivery Monitoring** - Implement email delivery tracking

### Email Templates
Templates are defined in `config/email.php`:
- **Password Reset Request** - Sent when user requests reset
- **Password Reset Confirmation** - Sent after successful reset

## Logging & Monitoring

All password reset activities are logged in the `system_logs` table:

### Log Types
- `Password Reset` - Reset request initiated
- `Password Reset Completed` - Password successfully changed
- `Email` - Email sending attempts and results

### Monitoring Queries
```sql
-- Recent password reset activity
SELECT * FROM system_logs 
WHERE type LIKE '%Password Reset%' 
ORDER BY created_at DESC 
LIMIT 50;

-- Failed email attempts
SELECT * FROM system_logs 
WHERE type = 'Email' AND description LIKE '%FAILED%' 
ORDER BY created_at DESC;
```

## Troubleshooting

### Common Issues

1. **Emails Not Sending**
   - Check PHP mail configuration
   - Verify server can send emails
   - Check spam folders
   - Review email logs in `system_logs`

2. **Token Expired Errors**
   - Tokens expire after 1 hour
   - User must request new reset link
   - Check system time synchronization

3. **Database Errors**
   - Verify table structure with setup script
   - Check database permissions
   - Review error logs

### Debug Mode
Enable debug logging by adding to your PHP configuration:
```php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);
```

## Security Considerations

### Production Deployment
1. **HTTPS Required** - All password reset pages must use HTTPS
2. **Email Security** - Use authenticated SMTP for email delivery
3. **Rate Limiting** - Consider implementing IP-based rate limiting
4. **Monitoring** - Set up alerts for suspicious password reset activity
5. **Backup** - Regular database backups including logs

### Regular Maintenance
1. **Log Cleanup** - Periodically clean old log entries
2. **Token Cleanup** - Clear expired tokens (handled automatically)
3. **Security Updates** - Keep PHP and dependencies updated
4. **Penetration Testing** - Regular security assessments

## Support

For issues or questions regarding the password reset system:
1. Check the `system_logs` table for error details
2. Review PHP error logs
3. Verify email server configuration
4. Test with known working email addresses

## Version Information

- **Created**: 2024
- **PHP Version**: 7.4+
- **Database**: MySQL 5.7+
- **Dependencies**: PDO, OpenSSL, Mail functions
