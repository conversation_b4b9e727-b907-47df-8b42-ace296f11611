<?php
/**
 * Password Reset System Table Creator
 * Run this file once to ensure required database tables exist
 */

// Include database connection
require_once 'connection/dbconnect.php';

echo "<h2>Password Reset System Setup</h2>\n";
echo "<p>Checking and creating required database tables...</p>\n";

try {
    // Check if system_logs table exists
    $stmt = $pdo->query("SHOW TABLES LIKE 'system_logs'");
    if ($stmt->rowCount() > 0) {
        echo "<p style='color: green;'>✓ Table 'system_logs' already exists!</p>\n";

        // Check if we need to expand field sizes for better logging
        $result = $pdo->query("DESCRIBE system_logs");
        $columns = [];
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
            $columns[$row['Field']] = $row['Type'];
        }

        if (isset($columns['type']) && $columns['type'] === 'varchar(20)') {
            echo "<p style='color: orange;'>ℹ️ Note: 'type' field is varchar(20) - consider expanding for better logging</p>\n";
        }

        if (isset($columns['description']) && $columns['description'] === 'varchar(100)') {
            echo "<p style='color: orange;'>ℹ️ Note: 'description' field is varchar(100) - logs will be truncated if needed</p>\n";
        }
    } else {
        // Create table if it doesn't exist
        $sql = "CREATE TABLE system_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            type VARCHAR(20) NOT NULL,
            description VARCHAR(100) NOT NULL,
            date TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

            INDEX idx_type (type),
            INDEX idx_date (date)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";

        $pdo->exec($sql);
        echo "<p style='color: green;'>✓ Table 'system_logs' created successfully!</p>\n";
    }

    // Verify operator_account table has required fields for password reset
    $result = $pdo->query("DESCRIBE operator_account");
    $columns = [];
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        $columns[] = $row['Field'];
    }

    $requiredFields = ['id', 'username', 'password', 'email', 'rememberToken', 'accountExpired', 'accountStatus'];
    $missingFields = array_diff($requiredFields, $columns);

    if (empty($missingFields)) {
        echo "<p style='color: green;'>✓ Table 'operator_account' has all required fields for password reset!</p>\n";
    } else {
        echo "<p style='color: red;'>✗ Missing fields in 'operator_account': " . implode(', ', $missingFields) . "</p>\n";
        echo "<p style='color: orange;'>Please ensure your operator_account table has these fields.</p>\n";
    }

    // Test the system_logs table by inserting a sample record
    $testSql = "INSERT INTO system_logs
                (type, description)
                VALUES ('Setup', 'Password reset system verified')";

    $pdo->exec($testSql);
    echo "<p style='color: green;'>✓ Test log entry created successfully!</p>\n";

    // Show operator_account table structure (relevant fields only)
    echo "<h3>Operator Account Table Structure (Password Reset Fields):</h3>\n";
    echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Purpose</th></tr>\n";

    $result = $pdo->query("DESCRIBE operator_account");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        if (in_array($row['Field'], $requiredFields)) {
            $purpose = '';
            switch ($row['Field']) {
                case 'id': $purpose = 'User identification'; break;
                case 'username': $purpose = 'Login username'; break;
                case 'password': $purpose = 'Hashed password'; break;
                case 'email': $purpose = 'Reset email destination'; break;
                case 'rememberToken': $purpose = 'Reset token storage'; break;
                case 'accountExpired': $purpose = 'Token expiry time'; break;
                case 'accountStatus': $purpose = 'Account status check'; break;
            }

            echo "<tr>";
            echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
            echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
            echo "<td>" . htmlspecialchars($purpose) . "</td>";
            echo "</tr>\n";
        }
    }
    echo "</table>\n";

    // Show system_logs table structure
    echo "<h3>System Logs Table Structure:</h3>\n";
    echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th><th>Extra</th></tr>\n";

    $result = $pdo->query("DESCRIBE system_logs");
    while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr>";
        echo "<td>" . htmlspecialchars($row['Field']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Type']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Null']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Key']) . "</td>";
        echo "<td>" . htmlspecialchars($row['Default'] ?? 'NULL') . "</td>";
        echo "<td>" . htmlspecialchars($row['Extra']) . "</td>";
        echo "</tr>\n";
    }
    echo "</table>\n";

    echo "<h3>Setup Complete!</h3>\n";
    echo "<p style='color: green;'>The password reset system is now ready to use.</p>\n";

    echo "<h4>Password Reset System Files:</h4>\n";
    echo "<ul>\n";
    echo "<li><strong>forgotpassword.php</strong> - Main forgot password form</li>\n";
    echo "<li><strong>inc.forgotpassword.php</strong> - Backend processing for reset requests</li>\n";
    echo "<li><strong>resetpassword.php</strong> - Password reset form (accessed via email link)</li>\n";
    echo "<li><strong>inc.resetpassword.php</strong> - Backend processing for new password</li>\n";
    echo "</ul>\n";

    echo "<h4>Security Features:</h4>\n";
    echo "<ul>\n";
    echo "<li>CSRF protection on all forms</li>\n";
    echo "<li>Rate limiting (3 attempts per hour)</li>\n";
    echo "<li>Secure token generation and hashing</li>\n";
    echo "<li>Token expiry (1 hour)</li>\n";
    echo "<li>Password strength validation</li>\n";
    echo "<li>Email confirmation</li>\n";
    echo "<li>Comprehensive logging</li>\n";
    echo "</ul>\n";

    echo "<p><strong>Important:</strong> You can now delete this file for security.</p>\n";
    echo "<p><a href='login.php'>← Back to Login</a> | <a href='forgotpassword.php'>Test Forgot Password →</a></p>\n";

} catch (PDOException $e) {
    echo "<p style='color: red;'>✗ Database Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
    echo "<p>Please check your database connection and try again.</p>\n";
} catch (Exception $e) {
    echo "<p style='color: red;'>✗ Error: " . htmlspecialchars($e->getMessage()) . "</p>\n";
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Password Reset System Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        table {
            border-collapse: collapse;
            margin: 10px 0;
            width: 100%;
        }
        th {
            background-color: #f0f0f0;
            padding: 8px;
            text-align: left;
        }
        td {
            padding: 8px;
            border-bottom: 1px solid #ddd;
        }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        h1, h2 { color: #333; }
        h3 { color: #666; margin-top: 30px; }
        ul { margin: 10px 0; }
        li { margin: 5px 0; }
        a {
            color: #007cba;
            text-decoration: none;
            padding: 8px 16px;
            background: #f0f8ff;
            border-radius: 4px;
            display: inline-block;
            margin: 5px;
        }
        a:hover { background: #e0f0ff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Password Reset System Setup</h1>
        <p>This script verifies and creates the required database tables for the password reset functionality.</p>

        <div style="background: #f9f9f9; padding: 15px; border-left: 4px solid #007cba; margin: 20px 0;">
            <h3>What this script does:</h3>
            <ul>
                <li>Checks/creates the <code>system_logs</code> table for logging</li>
                <li>Verifies the <code>operator_account</code> table has required fields</li>
                <li>Tests the tables with sample records</li>
                <li>Shows the table structures</li>
                <li>Provides system overview and security information</li>
            </ul>
        </div>
    </div>
</body>
</html>
