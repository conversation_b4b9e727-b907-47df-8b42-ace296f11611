<?php
/**
 * Simple Secure Configuration Setup
 * 
 * This creates a clean flow: .env → config.php → dbconnect.php
 * With security but simplified!
 */

// Security check
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowedIPs)) {
    die('Access denied. Run locally only.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Secure Setup</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto max-w-2xl px-4">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">🔒 Simple Secure Configuration</h1>
            
            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                <p class="text-sm text-blue-700">
                    <strong>Clean Flow:</strong> .env → config.php → dbconnect.php<br>
                    <strong>Secure:</strong> Encrypted credentials + Environment variables<br>
                    <strong>Simple:</strong> Easy to understand and maintain
                </p>
            </div>

            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                <div class="bg-gray-900 text-green-400 p-4 rounded-lg mb-6 font-mono text-sm">
                    <?php
                    try {
                        echo "🔧 Creating simplified secure configuration...\n\n";

                        // Step 1: Create .env file
                        echo "📝 Step 1: Creating .env file...\n";
                        $envContent = '# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=prod_calaguas
DB_USER=root
DB_PASSWORD=

# Application Settings
APP_ENV=development
APP_DEBUG=true

# Email Configuration
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Calaguas Booking System

# Security Settings
ENCRYPTION_KEY=f2c8e7b5ad23d4f901b2e3c4a8f7d6c7e5b4f3a1c2d8e9f0a4b6c8d7e5f3a2c1
ENCRYPTION_IV=1234567890123456';
                        
                        file_put_contents('.env', $envContent);
                        echo "✅ .env file created\n\n";

                        // Step 2: Create simplified config.php
                        echo "📝 Step 2: Creating simplified config.php...\n";
                        $configContent = '<?php
/**
 * Simplified Secure Configuration
 * 
 * Flow: .env → config.php → dbconnect.php
 * Loads from .env, encrypts sensitive data, provides to dbconnect.php
 */

// Simple environment loader
function loadEnv($file = \'.env\') {
    static $env = null;
    if ($env !== null) return $env;
    
    $env = [];
    if (file_exists($file)) {
        $lines = file($file, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($lines as $line) {
            if (strpos($line, \'=\') !== false && strpos($line, \'#\') !== 0) {
                list($key, $value) = explode(\'=\', $line, 2);
                $env[trim($key)] = trim($value);
            }
        }
    }
    return $env;
}

// Get environment variable
function env($key, $default = null) {
    $env = loadEnv();
    return isset($env[$key]) ? $env[$key] : $default;
}

// Simple encryption (for sensitive data)
function encryptValue($value) {
    $key = env(\'ENCRYPTION_KEY\');
    $iv = env(\'ENCRYPTION_IV\');
    if (!$key || !$iv) return $value; // Fallback to plain text
    return openssl_encrypt($value, \'aes-256-cbc\', $key, 0, $iv);
}

// Simple decryption
function decryptValue($value) {
    $key = env(\'ENCRYPTION_KEY\');
    $iv = env(\'ENCRYPTION_IV\');
    if (!$key || !$iv) return $value; // Fallback to plain text
    return openssl_decrypt($value, \'aes-256-cbc\', $key, 0, $iv);
}

// Database configuration (encrypted for security)
$config = [
    // Database settings (encrypted)
    \'db_host\' => encryptValue(env(\'DB_HOST\', \'localhost\')),
    \'db_port\' => env(\'DB_PORT\', \'3306\'),
    \'db_name\' => encryptValue(env(\'DB_NAME\', \'prod_calaguas\')),
    \'db_user\' => encryptValue(env(\'DB_USER\', \'root\')),
    \'db_password\' => encryptValue(env(\'DB_PASSWORD\', \'\')),
    \'db_charset\' => \'utf8mb4\',
    
    // Application settings (not encrypted)
    \'app_env\' => env(\'APP_ENV\', \'development\'),
    \'app_debug\' => env(\'APP_DEBUG\', \'false\') === \'true\',
    
    // Email settings (not encrypted)
    \'email_from\' => env(\'EMAIL_FROM_ADDRESS\', \'<EMAIL>\'),
    \'email_name\' => env(\'EMAIL_FROM_NAME\', \'Calaguas Booking System\'),
];

return $config;
?>';
                        file_put_contents('config/config.php', $configContent);
                        echo "✅ Simplified config.php created\n\n";

                        // Step 3: Create simplified dbconnect.php
                        echo "📝 Step 3: Creating simplified dbconnect.php...\n";
                        $dbContent = '<?php
/**
 * Simplified Database Connection
 * 
 * Gets configuration from config.php and connects to database
 */

// Load configuration
$config = require_once __DIR__ . \'/../config/config.php\';

// Decrypt database credentials
function decryptValue($value) {
    $envFile = __DIR__ . \'/../.env\';
    if (!file_exists($envFile)) return $value;
    
    $env = [];
    $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    foreach ($lines as $line) {
        if (strpos($line, \'=\') !== false && strpos($line, \'#\') !== 0) {
            list($key, $val) = explode(\'=\', $line, 2);
            $env[trim($key)] = trim($val);
        }
    }
    
    $key = $env[\'ENCRYPTION_KEY\'] ?? null;
    $iv = $env[\'ENCRYPTION_IV\'] ?? null;
    
    if (!$key || !$iv) return $value;
    return openssl_decrypt($value, \'aes-256-cbc\', $key, 0, $iv);
}

// Get database credentials
$host = decryptValue($config[\'db_host\']);
$port = $config[\'db_port\'];
$dbname = decryptValue($config[\'db_name\']);
$username = decryptValue($config[\'db_user\']);
$password = decryptValue($config[\'db_password\']);
$charset = $config[\'db_charset\'];

// Connect to database
try {
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=$charset";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_TIMEOUT => 5,
        PDO::MYSQL_ATTR_FOUND_ROWS => true,
        PDO::ATTR_PERSISTENT => false,
    ];
    
    $pdo = new PDO($dsn, $username, $password, $options);
    
    // Clear sensitive variables
    unset($host, $port, $username, $password, $dsn);
    
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    
    // Show detailed error in development
    if ($config[\'app_debug\']) {
        die("Database connection failed: " . $e->getMessage());
    } else {
        die("Database connection failed. Please try again later.");
    }
}
?>';
                        file_put_contents('connection/dbconnect.php', $dbContent);
                        echo "✅ Simplified dbconnect.php created\n\n";

                        // Step 4: Update email configuration
                        echo "📝 Step 4: Updating email configuration...\n";
                        $emailContent = '<?php
/**
 * Simplified Email Configuration
 * Uses config.php for settings
 */

// Load configuration
$config = require_once __DIR__ . \'/config.php\';

// Email settings
define(\'EMAIL_FROM_ADDRESS\', $config[\'email_from\']);
define(\'EMAIL_FROM_NAME\', $config[\'email_name\']);

// Email functions (simplified)
function sendPasswordResetEmail($to, $username, $resetLink) {
    $subject = "Password Reset Request - " . EMAIL_FROM_NAME;
    $message = getPasswordResetEmailTemplate($username, $resetLink);
    $headers = getEmailHeaders();
    return mail($to, $subject, $message, $headers);
}

function sendPasswordResetConfirmationEmail($to, $username) {
    $subject = "Password Reset Successful - " . EMAIL_FROM_NAME;
    $message = getPasswordResetConfirmationTemplate($username);
    $headers = getEmailHeaders();
    return mail($to, $subject, $message, $headers);
}

function getEmailHeaders() {
    return "MIME-Version: 1.0\r\n" .
           "Content-type:text/html;charset=UTF-8\r\n" .
           "From: " . EMAIL_FROM_NAME . " <" . EMAIL_FROM_ADDRESS . ">\r\n" .
           "Reply-To: " . EMAIL_FROM_ADDRESS . "\r\n";
}

function getPasswordResetEmailTemplate($username, $resetLink) {
    return "
    <html>
    <body style=\"font-family: Arial, sans-serif; padding: 20px;\">
        <h2 style=\"color: #3b82f6;\">🔐 Password Reset Request</h2>
        <p>Hello <strong>" . htmlspecialchars($username) . "</strong>,</p>
        <p>Click the link below to reset your password:</p>
        <p><a href=\"" . $resetLink . "\" style=\"background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\">Reset Password</a></p>
        <p>This link expires in 1 hour.</p>
        <p>If you did not request this, please ignore this email.</p>
    </body>
    </html>";
}

function getPasswordResetConfirmationTemplate($username) {
    return "
    <html>
    <body style=\"font-family: Arial, sans-serif; padding: 20px;\">
        <h2 style=\"color: #10b981;\">✅ Password Reset Successful</h2>
        <p>Hello <strong>" . htmlspecialchars($username) . "</strong>,</p>
        <p>Your password has been successfully reset.</p>
        <p>You can now log in with your new password.</p>
    </body>
    </html>";
}

function logEmailAttempt($type, $to, $success, $error = \"\") {
    global $pdo;
    try {
        $description = "Email: $type to $to";
        if (!$success) $description .= " - FAILED";
        if (strlen($description) > 100) $description = substr($description, 0, 97) . "...";
        
        $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (?, ?)");
        $stmt->execute(["Email", $description]);
    } catch (Exception $e) {
        error_log("Failed to log email: " . $e->getMessage());
    }
}
?>';
                        file_put_contents('config/email.php', $emailContent);
                        echo "✅ Simplified email.php created\n\n";

                        echo "🎉 Setup complete!\n\n";
                        echo "📁 File structure:\n";
                        echo "   .env (your settings)\n";
                        echo "   ↓\n";
                        echo "   config/config.php (loads .env, encrypts sensitive data)\n";
                        echo "   ↓\n";
                        echo "   connection/dbconnect.php (gets config, connects to DB)\n";
                        echo "   config/email.php (gets config, handles emails)\n\n";
                        echo "🔒 Security features:\n";
                        echo "   • Database credentials encrypted\n";
                        echo "   • .env file for easy configuration\n";
                        echo "   • Clean separation of concerns\n";
                        echo "   • Simple but secure\n";

                    } catch (Exception $e) {
                        echo "❌ Error: " . $e->getMessage();
                    }
                    ?>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                    <h3 class="text-sm font-medium text-green-800">✅ Setup Complete!</h3>
                    <p class="mt-2 text-sm text-green-700">
                        Your configuration is now simplified but secure. Update the .env file with your database password.
                    </p>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <h3 class="text-sm font-medium text-yellow-800">📝 Next Steps:</h3>
                    <ol class="mt-2 text-sm text-yellow-700 list-decimal list-inside">
                        <li>Edit .env file with your database password</li>
                        <li>Test the system</li>
                        <li>Delete this setup file (simple_secure_setup.php)</li>
                    </ol>
                </div>
                
            <?php else: ?>
                <div class="space-y-4">
                    <h3 class="text-lg font-semibold text-gray-800">🎯 What This Creates:</h3>
                    
                    <div class="bg-gray-50 rounded-lg p-4">
                        <div class="space-y-3">
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-bold">1</div>
                                <div>
                                    <strong>.env</strong> - Your configuration file (easy to edit)
                                </div>
                            </div>
                            <div class="ml-11 text-sm text-gray-600">
                                Contains database credentials, app settings, email config
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center text-sm font-bold">2</div>
                                <div>
                                    <strong>config/config.php</strong> - Loads .env and encrypts sensitive data
                                </div>
                            </div>
                            <div class="ml-11 text-sm text-gray-600">
                                Reads .env, encrypts database credentials for security
                            </div>
                            
                            <div class="flex items-center space-x-3">
                                <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center text-sm font-bold">3</div>
                                <div>
                                    <strong>connection/dbconnect.php</strong> - Gets config and connects
                                </div>
                            </div>
                            <div class="ml-11 text-sm text-gray-600">
                                Uses config.php, decrypts credentials, connects to database
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <h4 class="text-sm font-medium text-blue-800">🔒 Security Features:</h4>
                        <ul class="mt-2 text-sm text-blue-700 list-disc list-inside">
                            <li>Database credentials encrypted in memory</li>
                            <li>Environment variables for easy configuration</li>
                            <li>Clean separation of concerns</li>
                            <li>Simple but secure approach</li>
                        </ul>
                    </div>
                    
                    <form method="POST" class="text-center">
                        <button 
                            type="submit" 
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700"
                        >
                            🔒 Create Simplified Secure Configuration
                        </button>
                    </form>
                </div>
            <?php endif; ?>
            
            <div class="mt-6 text-center">
                <a href="forgotpassword.php" class="text-blue-600 hover:underline">Test Forgot Password</a>
                <span class="mx-2">|</span>
                <a href="login.php" class="text-blue-600 hover:underline">Back to Login</a>
            </div>
        </div>
    </div>
</body>
</html>
