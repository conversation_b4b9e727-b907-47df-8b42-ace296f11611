<?php
/**
 * Deployment Script for Simple Email Configuration
 * 
 * This script switches the forgot password system to use the simple email
 * configuration (PHP mail() function) instead of SMTP for production deployment.
 */

// Security check - only allow access from localhost
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowedIPs) && !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1'])) {
    die('Access denied. This deployment script can only be run locally.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deploy Simple Email Configuration</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto max-w-4xl px-4">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">🚀 Deploy Simple Email Configuration</h1>
            
            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            <strong>Purpose:</strong> This script switches the forgot password system from SMTP to simple PHP mail() function for production deployment.
                        </p>
                    </div>
                </div>
            </div>

            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['deploy'])): ?>
                <div class="bg-gray-900 text-green-400 p-4 rounded-lg mb-6 font-mono text-sm">
                    <div class="mb-2">🔄 Starting deployment process...</div>
                    
                    <?php
                    $success = true;
                    $errors = [];
                    
                    try {
                        // Step 1: Backup current files
                        echo "<div>📦 Creating backups...</div>";
                        
                        if (file_exists('inc.forgotpassword.php')) {
                            if (copy('inc.forgotpassword.php', 'inc.forgotpassword_smtp_backup.php')) {
                                echo "<div>✅ Backed up inc.forgotpassword.php</div>";
                            } else {
                                throw new Exception("Failed to backup inc.forgotpassword.php");
                            }
                        }
                        
                        if (file_exists('inc.resetpassword.php')) {
                            if (copy('inc.resetpassword.php', 'inc.resetpassword_smtp_backup.php')) {
                                echo "<div>✅ Backed up inc.resetpassword.php</div>";
                            } else {
                                throw new Exception("Failed to backup inc.resetpassword.php");
                            }
                        }
                        
                        if (file_exists('config/email.php')) {
                            if (copy('config/email.php', 'config/email_smtp_backup.php')) {
                                echo "<div>✅ Backed up config/email.php</div>";
                            } else {
                                throw new Exception("Failed to backup config/email.php");
                            }
                        }
                        
                        // Step 2: Replace files with simple versions
                        echo "<div class='mt-2'>🔄 Deploying simple email configuration...</div>";
                        
                        if (file_exists('inc.forgotpassword_simple.php')) {
                            if (copy('inc.forgotpassword_simple.php', 'inc.forgotpassword.php')) {
                                echo "<div>✅ Deployed inc.forgotpassword.php</div>";
                            } else {
                                throw new Exception("Failed to deploy inc.forgotpassword.php");
                            }
                        } else {
                            throw new Exception("inc.forgotpassword_simple.php not found");
                        }
                        
                        if (file_exists('inc.resetpassword_simple.php')) {
                            if (copy('inc.resetpassword_simple.php', 'inc.resetpassword.php')) {
                                echo "<div>✅ Deployed inc.resetpassword.php</div>";
                            } else {
                                throw new Exception("Failed to deploy inc.resetpassword.php");
                            }
                        } else {
                            throw new Exception("inc.resetpassword_simple.php not found");
                        }
                        
                        if (file_exists('config/email_simple.php')) {
                            if (copy('config/email_simple.php', 'config/email.php')) {
                                echo "<div>✅ Deployed config/email.php</div>";
                            } else {
                                throw new Exception("Failed to deploy config/email.php");
                            }
                        } else {
                            throw new Exception("config/email_simple.php not found");
                        }
                        
                        // Step 3: Clean up development files
                        echo "<div class='mt-2'>🧹 Cleaning up development files...</div>";
                        
                        $filesToRemove = [
                            'test_smtp.php',
                            'create_password_reset_tables.php',
                            'deploy_simple_email.php' // This script itself
                        ];
                        
                        foreach ($filesToRemove as $file) {
                            if (file_exists($file)) {
                                echo "<div>🗑️ Marked for removal: $file</div>";
                            }
                        }
                        
                        echo "<div class='mt-2 text-green-300'>✅ Deployment completed successfully!</div>";
                        
                    } catch (Exception $e) {
                        $success = false;
                        $errors[] = $e->getMessage();
                        echo "<div class='text-red-400'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                    ?>
                </div>
                
                <?php if ($success): ?>
                    <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">Deployment Successful!</h3>
                                <div class="mt-2 text-sm text-green-700">
                                    <p>The forgot password system has been successfully configured for production deployment.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">Next Steps</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <ul class="list-disc list-inside space-y-1">
                                        <li>Test the forgot password functionality</li>
                                        <li>Configure your server's mail settings if needed</li>
                                        <li>Delete the following files for security:
                                            <ul class="list-disc list-inside ml-4 mt-1">
                                                <li><code>test_smtp.php</code></li>
                                                <li><code>create_password_reset_tables.php</code></li>
                                                <li><code>deploy_simple_email.php</code> (this file)</li>
                                            </ul>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Deployment Failed</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <p>The deployment encountered errors. Please check the console output above and try again.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">📋 What this deployment does:</h3>
                        <ul class="list-disc list-inside space-y-2 text-gray-700">
                            <li>Backs up current SMTP-enabled files</li>
                            <li>Replaces them with simple PHP mail() versions</li>
                            <li>Removes development and testing files</li>
                            <li>Prepares the system for production deployment</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">📁 Files that will be modified:</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <strong>Replaced:</strong>
                                    <ul class="mt-1 space-y-1 text-gray-600">
                                        <li>• inc.forgotpassword.php</li>
                                        <li>• inc.resetpassword.php</li>
                                        <li>• config/email.php</li>
                                    </ul>
                                </div>
                                <div>
                                    <strong>Removed:</strong>
                                    <ul class="mt-1 space-y-1 text-gray-600">
                                        <li>• test_smtp.php</li>
                                        <li>• create_password_reset_tables.php</li>
                                        <li>• deploy_simple_email.php</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-red-50 border border-red-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">⚠️ Warning</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <p>This action will modify your files. Backups will be created, but please ensure you have your own backups before proceeding.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" class="text-center">
                        <button 
                            type="submit" 
                            name="deploy"
                            value="1"
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                            onclick="return confirm('Are you sure you want to deploy the simple email configuration? This will modify your files.')"
                        >
                            🚀 Deploy Simple Email Configuration
                        </button>
                    </form>
                </div>
            <?php endif; ?>
            
            <div class="mt-8 text-center">
                <a href="forgotpassword.php" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    🔑 Test Forgot Password
                </a>
                <a href="login.php" class="ml-4 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    🏠 Back to Login
                </a>
            </div>
        </div>
    </div>
</body>
</html>
