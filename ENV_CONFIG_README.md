# Environment-Based Configuration Guide

## Overview

This guide explains how to use the environment-based configuration system for the Calaguas Booking System. This approach uses a `.env` file to store sensitive configuration data, making deployment easier and more secure.

## Quick Setup

### Option 1: Automated Deployment (Recommended)

1. **Run the deployment script**:
   ```bash
   http://yourdomain.com/deploy_env_config.php
   ```

2. **Click "Deploy Environment Configuration"**

3. **Update your .env file** with correct values

4. **Delete deployment script** for security

### Option 2: Manual Setup

1. **Copy environment files**:
   ```bash
   cp .env.example .env
   cp connection/dbconnect_env.php connection/dbconnect.php
   cp config/email_env.php config/email.php
   cp inc.forgotpassword_env.php inc.forgotpassword.php
   cp inc.resetpassword_env.php inc.resetpassword.php
   ```

2. **Edit .env file** with your configuration

## Environment Configuration

### .env File Structure

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_NAME=prod_calaguas
DB_USER=root
DB_PASSWORD=your_password
DB_CHARSET=utf8mb4

# Application Settings
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

# Email Configuration
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Calaguas Booking System
EMAIL_REPLY_TO=<EMAIL>

# SMTP Configuration (Optional)
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=465
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=4>v4s@TWg~K
SMTP_ENCRYPTION=ssl

# Security Settings
SESSION_LIFETIME=3600
CSRF_TOKEN_LIFETIME=3600
PASSWORD_RESET_TOKEN_LIFETIME=3600
MAX_RESET_ATTEMPTS=3

# System Settings
SYSTEM_MAINTENANCE=false
LOG_LEVEL=info
```

### Environment Variables Reference

#### Database Settings
- `DB_HOST` - Database server hostname
- `DB_PORT` - Database server port (default: 3306)
- `DB_NAME` - Database name
- `DB_USER` - Database username
- `DB_PASSWORD` - Database password
- `DB_CHARSET` - Database character set (default: utf8mb4)

#### Application Settings
- `APP_ENV` - Environment (development/staging/production)
- `APP_DEBUG` - Enable debug mode (true/false)
- `APP_URL` - Base URL of your application

#### Email Settings
- `EMAIL_FROM_ADDRESS` - From email address
- `EMAIL_FROM_NAME` - From name for emails
- `EMAIL_REPLY_TO` - Reply-to email address
- `EMAIL_METHOD` - Email method (mail/smtp)

#### SMTP Settings (Optional)
- `SMTP_HOST` - SMTP server hostname
- `SMTP_PORT` - SMTP server port
- `SMTP_USERNAME` - SMTP username
- `SMTP_PASSWORD` - SMTP password
- `SMTP_ENCRYPTION` - Encryption method (ssl/tls)

#### Security Settings
- `SESSION_LIFETIME` - Session timeout in seconds
- `CSRF_TOKEN_LIFETIME` - CSRF token lifetime in seconds
- `PASSWORD_RESET_TOKEN_LIFETIME` - Reset token lifetime in seconds
- `MAX_RESET_ATTEMPTS` - Maximum reset attempts per hour

#### Password Requirements
- `MIN_PASSWORD_LENGTH` - Minimum password length (default: 8)
- `PASSWORD_REQUIRE_UPPERCASE` - Require uppercase letters (true/false)
- `PASSWORD_REQUIRE_LOWERCASE` - Require lowercase letters (true/false)
- `PASSWORD_REQUIRE_NUMBERS` - Require numbers (true/false)
- `PASSWORD_REQUIRE_SPECIAL` - Require special characters (true/false)

## Benefits of Environment Configuration

### 🔒 Security
- Sensitive data not stored in code
- Different credentials for different environments
- Easy to rotate secrets without code changes

### 🚀 Deployment
- Same codebase for all environments
- Environment-specific configuration
- No need to modify code for deployment

### 🔧 Flexibility
- Easy to change settings without code changes
- Support for different email methods
- Configurable security settings

### 👥 Team Collaboration
- Each developer can have their own .env
- No conflicts with different local settings
- Shared .env.example for reference

## Environment-Specific Configurations

### Development Environment
```bash
APP_ENV=development
APP_DEBUG=true
APP_URL=http://localhost

DB_HOST=localhost
DB_USER=root
DB_PASSWORD=

EMAIL_METHOD=mail
```

### Staging Environment
```bash
APP_ENV=staging
APP_DEBUG=true
APP_URL=https://staging.yourdomain.com

DB_HOST=staging-db.yourdomain.com
DB_USER=staging_user
DB_PASSWORD=staging_password

EMAIL_METHOD=smtp
SMTP_HOST=smtp.hostinger.com
```

### Production Environment
```bash
APP_ENV=production
APP_DEBUG=false
APP_URL=https://yourdomain.com

DB_HOST=prod-db.yourdomain.com
DB_USER=prod_user
DB_PASSWORD=secure_production_password

EMAIL_METHOD=smtp
SMTP_HOST=smtp.hostinger.com
```

## File Structure After Deployment

```
/
├── .env                           # Environment configuration
├── .env.example                   # Environment template
├── .gitignore                     # Git ignore rules
├── config/
│   ├── env.php                    # Environment loader
│   └── email.php                  # Email configuration (env-based)
├── connection/
│   └── dbconnect.php              # Database connection (env-based)
├── inc.forgotpassword.php         # Forgot password processing (env-based)
├── inc.resetpassword.php          # Reset password processing (env-based)
└── *_backup.php                   # Backup files
```

## Usage in Code

### Accessing Environment Variables

```php
// Load environment configuration
require_once 'config/env.php';

// Get environment variables with defaults
$dbHost = env('DB_HOST', 'localhost');
$debug = env('APP_DEBUG', false);
$maxAttempts = env('MAX_RESET_ATTEMPTS', 3);

// Check if variable exists
if (env('SMTP_PASSWORD')) {
    // Use SMTP
} else {
    // Use simple mail
}
```

### Environment Helper Function

```php
// Get value with default
$value = env('VARIABLE_NAME', 'default_value');

// Boolean values are automatically parsed
$debug = env('APP_DEBUG'); // Returns true/false, not string

// Numeric values are automatically parsed
$port = env('DB_PORT'); // Returns integer, not string
```

## Security Best Practices

### 🔐 .env File Security
- Never commit .env to version control
- Use different .env files for each environment
- Restrict file permissions (600 or 644)
- Regularly rotate sensitive credentials

### 🛡️ Production Security
- Set `APP_DEBUG=false` in production
- Use strong database passwords
- Enable HTTPS for production
- Use environment-specific SMTP credentials

### 🔄 Credential Rotation
```bash
# Update .env file
DB_PASSWORD=new_secure_password
SMTP_PASSWORD=new_smtp_password

# Restart application if needed
# No code changes required
```

## Troubleshooting

### Common Issues

**Issue**: Environment variables not loading
```bash
# Check if .env file exists and is readable
ls -la .env

# Check file permissions
chmod 644 .env
```

**Issue**: Database connection fails
```bash
# Verify database credentials in .env
DB_HOST=correct_host
DB_USER=correct_user
DB_PASSWORD=correct_password
DB_NAME=correct_database
```

**Issue**: Email not sending
```bash
# Check email configuration
EMAIL_METHOD=mail  # or smtp
SMTP_HOST=your.smtp.host
SMTP_USERNAME=your_username
SMTP_PASSWORD=your_password
```

### Debug Mode

Enable debug mode for troubleshooting:
```bash
APP_DEBUG=true
```

This will show detailed error messages instead of generic ones.

## Migration from Encrypted Config

If you're migrating from the encrypted configuration system:

1. **Backup current configuration**
2. **Run deployment script** or copy files manually
3. **Update .env file** with your actual credentials
4. **Test the system** thoroughly
5. **Remove old encrypted files** if everything works

## Version Control

### .gitignore Configuration
The `.gitignore` file excludes:
- `.env` files
- Backup files
- Development/testing scripts
- Sensitive configuration files

### Team Setup
1. **Share .env.example** with team members
2. **Each developer creates their own .env**
3. **Document environment-specific settings**
4. **Use consistent variable names across environments**

## Support

For issues with environment configuration:
1. Check `.env` file syntax and values
2. Verify file permissions and accessibility
3. Review error logs for detailed messages
4. Test with debug mode enabled
5. Compare with `.env.example` for reference
