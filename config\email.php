<?php
/**
 * Email Configuration for Password Reset System
 *
 * This file contains email settings and helper functions for sending
 * password reset emails and other system notifications.
 */

// SMTP Configuration
define('SMTP_HOST', 'smtp.hostinger.com');
define('SMTP_PORT', 465);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', '4>v4s@TWg~K');
define('SMTP_ENCRYPTION', 'ssl'); // 'ssl' for port 465, 'tls' for port 587

// Email configuration constants
define('EMAIL_FROM_ADDRESS', '<EMAIL>');
define('EMAIL_FROM_NAME', 'Calaguas Booking System');
define('EMAIL_REPLY_TO', '<EMAIL>');

// Email templates and styling
define('EMAIL_HEADER_COLOR', '#3b82f6');
define('EMAIL_SUCCESS_COLOR', '#10b981');
define('EMAIL_WARNING_COLOR', '#f59e0b');

/**
 * Send email via SMTP
 *
 * @param string $to Recipient email address
 * @param string $subject Email subject
 * @param string $message HTML email message
 * @return bool True if email was sent successfully
 */
function sendSMTPEmail($to, $subject, $message) {
    try {
        // Create SSL context
        $context = stream_context_create([
            'ssl' => [
                'verify_peer' => false,
                'verify_peer_name' => false,
                'allow_self_signed' => true
            ]
        ]);

        // Connect to SMTP server
        $smtp = stream_socket_client(
            'ssl://' . SMTP_HOST . ':' . SMTP_PORT,
            $errno,
            $errstr,
            30,
            STREAM_CLIENT_CONNECT,
            $context
        );

        if (!$smtp) {
            error_log("SMTP Connection failed: $errstr ($errno)");
            return false;
        }

        // Read server greeting
        $response = fgets($smtp, 512);
        if (substr($response, 0, 3) !== '220') {
            error_log("SMTP Error: Unexpected greeting - $response");
            fclose($smtp);
            return false;
        }

        // Send EHLO
        fputs($smtp, "EHLO " . $_SERVER['HTTP_HOST'] . "\r\n");
        $response = fgets($smtp, 512);
        if (substr($response, 0, 3) !== '250') {
            error_log("SMTP Error: EHLO failed - $response");
            fclose($smtp);
            return false;
        }

        // Skip additional EHLO responses
        while (substr($response, 3, 1) === '-') {
            $response = fgets($smtp, 512);
        }

        // Authenticate
        fputs($smtp, "AUTH LOGIN\r\n");
        $response = fgets($smtp, 512);
        if (substr($response, 0, 3) !== '334') {
            error_log("SMTP Error: AUTH LOGIN failed - $response");
            fclose($smtp);
            return false;
        }

        // Send username
        fputs($smtp, base64_encode(SMTP_USERNAME) . "\r\n");
        $response = fgets($smtp, 512);
        if (substr($response, 0, 3) !== '334') {
            error_log("SMTP Error: Username authentication failed - $response");
            fclose($smtp);
            return false;
        }

        // Send password
        fputs($smtp, base64_encode(SMTP_PASSWORD) . "\r\n");
        $response = fgets($smtp, 512);
        if (substr($response, 0, 3) !== '235') {
            error_log("SMTP Error: Password authentication failed - $response");
            fclose($smtp);
            return false;
        }

        // Set sender
        fputs($smtp, "MAIL FROM: <" . EMAIL_FROM_ADDRESS . ">\r\n");
        $response = fgets($smtp, 512);
        if (substr($response, 0, 3) !== '250') {
            error_log("SMTP Error: MAIL FROM failed - $response");
            fclose($smtp);
            return false;
        }

        // Set recipient
        fputs($smtp, "RCPT TO: <$to>\r\n");
        $response = fgets($smtp, 512);
        if (substr($response, 0, 3) !== '250') {
            error_log("SMTP Error: RCPT TO failed - $response");
            fclose($smtp);
            return false;
        }

        // Start data
        fputs($smtp, "DATA\r\n");
        $response = fgets($smtp, 512);
        if (substr($response, 0, 3) !== '354') {
            error_log("SMTP Error: DATA command failed - $response");
            fclose($smtp);
            return false;
        }

        // Send headers and message
        $headers = "From: " . EMAIL_FROM_NAME . " <" . EMAIL_FROM_ADDRESS . ">\r\n";
        $headers .= "Reply-To: " . EMAIL_REPLY_TO . "\r\n";
        $headers .= "To: $to\r\n";
        $headers .= "Subject: $subject\r\n";
        $headers .= "MIME-Version: 1.0\r\n";
        $headers .= "Content-Type: text/html; charset=UTF-8\r\n";
        $headers .= "X-Mailer: Calaguas Booking System\r\n";
        $headers .= "\r\n";

        fputs($smtp, $headers . $message . "\r\n.\r\n");
        $response = fgets($smtp, 512);
        if (substr($response, 0, 3) !== '250') {
            error_log("SMTP Error: Message sending failed - $response");
            fclose($smtp);
            return false;
        }

        // Quit
        fputs($smtp, "QUIT\r\n");
        fclose($smtp);

        return true;

    } catch (Exception $e) {
        error_log("SMTP Exception: " . $e->getMessage());
        return false;
    }
}

/**
 * Send password reset email
 *
 * @param string $to Recipient email address
 * @param string $username Username for personalization
 * @param string $resetLink Password reset link
 * @return bool True if email was sent successfully
 */
function sendPasswordResetEmail($to, $username, $resetLink) {
    $subject = "Password Reset Request - Calaguas Booking System";
    $message = getPasswordResetEmailTemplate($username, $resetLink);

    return sendSMTPEmail($to, $subject, $message);
}

/**
 * Send password reset confirmation email
 *
 * @param string $to Recipient email address
 * @param string $username Username for personalization
 * @return bool True if email was sent successfully
 */
function sendPasswordResetConfirmationEmail($to, $username) {
    $subject = "Password Reset Successful - Calaguas Booking System";
    $message = getPasswordResetConfirmationTemplate($username);

    return sendSMTPEmail($to, $subject, $message);
}

/**
 * Get email headers for HTML emails
 *
 * @return string Email headers
 */
function getEmailHeaders() {
    $headers = "MIME-Version: 1.0" . "\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8" . "\r\n";
    $headers .= "From: " . EMAIL_FROM_NAME . " <" . EMAIL_FROM_ADDRESS . ">" . "\r\n";
    $headers .= "Reply-To: " . EMAIL_REPLY_TO . "\r\n";
    $headers .= "X-Mailer: PHP/" . phpversion() . "\r\n";

    return $headers;
}

/**
 * Get password reset email template
 *
 * @param string $username Username for personalization
 * @param string $resetLink Password reset link
 * @return string HTML email template
 */
function getPasswordResetEmailTemplate($username, $resetLink) {
    $baseUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']);

    return "
    <html>
    <head>
        <title>Password Reset Request</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
                background-color: #f4f4f4;
            }
            .container {
                max-width: 600px;
                margin: 20px auto;
                background: white;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .header {
                background-color: " . EMAIL_HEADER_COLOR . ";
                color: white;
                padding: 30px 20px;
                text-align: center;
            }
            .header h1 {
                margin: 0;
                font-size: 24px;
            }
            .content {
                padding: 30px 20px;
                background-color: #ffffff;
            }
            .button {
                display: inline-block;
                padding: 15px 30px;
                background-color: " . EMAIL_HEADER_COLOR . ";
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 20px 0;
                font-weight: bold;
                text-align: center;
            }
            .button:hover {
                background-color: #2563eb;
            }
            .footer {
                padding: 20px;
                text-align: center;
                font-size: 12px;
                color: #666;
                background-color: #f8f9fa;
            }
            .security-notice {
                background-color: #fef3c7;
                border-left: 4px solid " . EMAIL_WARNING_COLOR . ";
                padding: 15px;
                margin: 20px 0;
                border-radius: 4px;
            }
            .link-text {
                word-break: break-all;
                background-color: #f8f9fa;
                padding: 10px;
                border-radius: 4px;
                font-family: monospace;
                font-size: 12px;
            }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>🔐 Password Reset Request</h1>
            </div>
            <div class='content'>
                <p>Hello <strong>" . htmlspecialchars($username) . "</strong>,</p>
                <p>We received a request to reset your password for your Calaguas Booking System account.</p>
                <p>Click the button below to reset your password:</p>
                <p style='text-align: center;'>
                    <a href='" . $resetLink . "' class='button'>🔑 Reset Password</a>
                </p>
                <p>Or copy and paste this link into your browser:</p>
                <div class='link-text'>" . $resetLink . "</div>

                <div class='security-notice'>
                    <strong>⚠️ Security Notice:</strong>
                    <ul>
                        <li>This link will expire in <strong>1 hour</strong> for security reasons</li>
                        <li>If you didn't request this password reset, please ignore this email</li>
                        <li>Your password will remain unchanged if you don't click the link</li>
                    </ul>
                </div>

                <p>If you're having trouble clicking the button, you can also visit the password reset page directly at:</p>
                <p><a href='" . $baseUrl . "/forgotpassword.php'>" . $baseUrl . "/forgotpassword.php</a></p>
            </div>
            <div class='footer'>
                <p>This is an automated message from Calaguas Booking System.</p>
                <p>Please do not reply to this email.</p>
                <p>© " . date('Y') . " Calaguas Booking System. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    ";
}

/**
 * Get password reset confirmation email template
 *
 * @param string $username Username for personalization
 * @return string HTML email template
 */
function getPasswordResetConfirmationTemplate($username) {
    $baseUrl = "http://" . $_SERVER['HTTP_HOST'] . dirname($_SERVER['PHP_SELF']);

    return "
    <html>
    <head>
        <title>Password Reset Successful</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
                background-color: #f4f4f4;
            }
            .container {
                max-width: 600px;
                margin: 20px auto;
                background: white;
                border-radius: 8px;
                overflow: hidden;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }
            .header {
                background-color: " . EMAIL_SUCCESS_COLOR . ";
                color: white;
                padding: 30px 20px;
                text-align: center;
            }
            .header h1 {
                margin: 0;
                font-size: 24px;
            }
            .content {
                padding: 30px 20px;
                background-color: #ffffff;
            }
            .button {
                display: inline-block;
                padding: 15px 30px;
                background-color: " . EMAIL_SUCCESS_COLOR . ";
                color: white;
                text-decoration: none;
                border-radius: 5px;
                margin: 20px 0;
                font-weight: bold;
                text-align: center;
            }
            .footer {
                padding: 20px;
                text-align: center;
                font-size: 12px;
                color: #666;
                background-color: #f8f9fa;
            }
            .security-notice {
                background-color: #fef3c7;
                border-left: 4px solid " . EMAIL_WARNING_COLOR . ";
                padding: 15px;
                margin: 20px 0;
                border-radius: 4px;
            }
        </style>
    </head>
    <body>
        <div class='container'>
            <div class='header'>
                <h1>✅ Password Reset Successful</h1>
            </div>
            <div class='content'>
                <p>Hello <strong>" . htmlspecialchars($username) . "</strong>,</p>
                <p>Your password has been successfully reset for your Calaguas Booking System account.</p>
                <p>You can now log in with your new password:</p>
                <p style='text-align: center;'>
                    <a href='" . $baseUrl . "/login.php' class='button'>🚪 Login Now</a>
                </p>

                <div class='security-notice'>
                    <strong>🔒 Security Notice:</strong>
                    <ul>
                        <li>If you did not reset your password, please contact support immediately</li>
                        <li>For security reasons, we recommend using a strong, unique password</li>
                        <li>Never share your password with anyone</li>
                        <li>Consider enabling two-factor authentication if available</li>
                    </ul>
                </div>

                <p><strong>Reset completed on:</strong> " . date('Y-m-d H:i:s') . "</p>
                <p>If you experience any issues logging in, please contact our support team.</p>
            </div>
            <div class='footer'>
                <p>This is an automated message from Calaguas Booking System.</p>
                <p>Please do not reply to this email.</p>
                <p>© " . date('Y') . " Calaguas Booking System. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    ";
}

/**
 * Log email sending attempts
 *
 * @param string $type Type of email (e.g., 'Password Reset', 'Password Reset Confirmation')
 * @param string $to Recipient email
 * @param bool $success Whether email was sent successfully
 * @param string $error Error message if failed
 */
function logEmailAttempt($type, $to, $success, $error = '') {
    global $pdo;

    try {
        // Truncate description to fit varchar(100) limit
        $description = "Email: $type to $to";
        if (!$success) {
            $description .= " - FAILED";
        }

        // Ensure description fits in varchar(100)
        if (strlen($description) > 100) {
            $description = substr($description, 0, 97) . '...';
        }

        $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
        $stmt->execute([
            ':type' => 'Email',
            ':description' => $description
        ]);
    } catch (Exception $e) {
        error_log("Failed to log email attempt: " . $e->getMessage());
    }
}
?>
