<?php
// Start the session with secure cookie parameters
session_start([
    'cookie_secure'   => true,       // Only send cookie over HTTPS
    'cookie_httponly' => true,       // <PERSON><PERSON> cannot access the cookie
    'cookie_samesite' => 'Strict'    // Prevent CSRF in modern browsers
]);

include 'connection/dbconnect_env.php';
include 'config/email_env.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    // Clear any previous errors
    unset($_SESSION['error']);

    $token = trim($_POST['token'] ?? '');
    $password = trim($_POST['password'] ?? '');
    $confirm_password = trim($_POST['confirm_password'] ?? '');

    try {
        // Ensure CSRF tokens exist and compare them in constant time
        if (
            empty($_SESSION['csrf_token']) || empty($_POST['csrf_token']) ||
            !hash_equals($_SESSION['csrf_token'], $_POST['csrf_token'])
        ) {
            throw new Exception("Invalid request. Please try again.");
        }

        // Validate required fields
        if (empty($token) || empty($password) || empty($confirm_password)) {
            throw new Exception("All fields are required.");
        }

        // Get password requirements from environment
        $minPasswordLength = env('MIN_PASSWORD_LENGTH', 8);
        $requireUppercase = env('PASSWORD_REQUIRE_UPPERCASE', true);
        $requireLowercase = env('PASSWORD_REQUIRE_LOWERCASE', true);
        $requireNumbers = env('PASSWORD_REQUIRE_NUMBERS', true);
        $requireSpecialChars = env('PASSWORD_REQUIRE_SPECIAL', false);

        // Password complexity check
        if (strlen($password) < $minPasswordLength) {
            throw new Exception("Password must be at least $minPasswordLength characters long.");
        }

        // Ensure passwords match
        if ($password !== $confirm_password) {
            throw new Exception("Passwords do not match.");
        }

        // Additional password strength validation
        $passwordErrors = [];
        
        if ($requireUppercase && !preg_match('/[A-Z]/', $password)) {
            $passwordErrors[] = "one uppercase letter";
        }
        
        if ($requireLowercase && !preg_match('/[a-z]/', $password)) {
            $passwordErrors[] = "one lowercase letter";
        }
        
        if ($requireNumbers && !preg_match('/\d/', $password)) {
            $passwordErrors[] = "one number";
        }
        
        if ($requireSpecialChars && !preg_match('/[^A-Za-z0-9]/', $password)) {
            $passwordErrors[] = "one special character";
        }

        if (!empty($passwordErrors)) {
            throw new Exception("Password must contain at least " . implode(", ", $passwordErrors) . ".");
        }

        // Hash the token to compare with database
        $hashedToken = hash('sha256', $token);
        
        // Check if token exists and is not expired
        $stmt = $pdo->prepare("SELECT id, username, email, accountExpired FROM operator_account WHERE rememberToken = :token AND accountStatus = 'Activated'");
        $stmt->execute([':token' => $hashedToken]);
        $user = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$user) {
            throw new Exception("Invalid or expired reset token.");
        }

        // Check if token has expired
        $expiry = strtotime($user['accountExpired']);
        if (!$expiry || $expiry <= time()) {
            throw new Exception("Reset token has expired. Please request a new password reset.");
        }

        // Start transaction
        $pdo->beginTransaction();

        // Hash the new password
        $passwordAlgorithm = env('PASSWORD_HASH_ALGORITHM', PASSWORD_BCRYPT);
        $hashed_password = password_hash($password, $passwordAlgorithm);

        // Update the password and clear the reset token
        $stmt = $pdo->prepare("UPDATE operator_account SET password = :password, rememberToken = NULL, accountExpired = NULL WHERE id = :id");
        $stmt->execute([
            ':password' => $hashed_password,
            ':id' => $user['id']
        ]);

        if ($stmt->rowCount() === 0) {
            throw new Exception('Failed to update password. Please try again.');
        }

        // Log the password reset completion
        $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
        $stmt->execute([
            ':type' => 'Reset Complete',
            ':description' => 'Password reset for: ' . $user['username'] . ' (ID: ' . $user['id'] . ')'
        ]);

        // Commit the transaction
        $pdo->commit();

        // Send confirmation email if enabled
        $sendConfirmationEmail = env('SEND_PASSWORD_RESET_CONFIRMATION', true);
        if ($sendConfirmationEmail) {
            $emailSent = sendPasswordResetConfirmationEmail($user['email'], $user['username']);

            // Log email sending attempt
            logEmailAttempt('Password Reset Confirmation', $user['email'], $emailSent, $emailSent ? '' : 'Mail function failed');

            if (!$emailSent) {
                error_log("Failed to send password reset confirmation email to: " . $user['email']);
            }
        }

        // Set success message and redirect to login
        $_SESSION['success'] = "Your password has been successfully reset. You can now log in with your new password.";
        header("Location: login.php");
        exit();

    } catch (Exception $e) {
        // Rollback on error
        if ($pdo->inTransaction()) {
            $pdo->rollBack();
        }
        
        $_SESSION['error'] = $e->getMessage();
        
        // Redirect back to reset form with token
        if (!empty($token)) {
            header("Location: resetpassword.php?token=" . urlencode($token));
        } else {
            header("Location: forgotpassword.php");
        }
        exit();
    }
} else {
    // Redirect if not POST request
    header("Location: forgotpassword.php");
    exit();
}
?>
