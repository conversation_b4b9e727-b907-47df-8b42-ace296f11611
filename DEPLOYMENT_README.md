# Forgot Password System - Production Deployment Guide

## Overview

This guide explains how to deploy the forgot password system for production use with simple PHP mail() function instead of SMTP.

## Quick Deployment

### Option 1: Automated Deployment (Recommended)

1. **Run the deployment script**:
   ```bash
   http://yourdomain.com/deploy_simple_email.php
   ```

2. **Click "Deploy Simple Email Configuration"**

3. **Delete security files** after deployment:
   - `test_smtp.php`
   - `create_password_reset_tables.php` 
   - `deploy_simple_email.php`

### Option 2: Manual Deployment

1. **Replace email configuration**:
   ```bash
   cp config/email_simple.php config/email.php
   ```

2. **Replace processing files**:
   ```bash
   cp inc.forgotpassword_simple.php inc.forgotpassword.php
   cp inc.resetpassword_simple.php inc.resetpassword.php
   ```

3. **Delete development files**:
   ```bash
   rm test_smtp.php
   rm create_password_reset_tables.php
   rm deploy_simple_email.php
   ```

## Production Configuration

### Email Settings

The production version uses these settings in `config/email.php`:

```php
// Email configuration constants
define('EMAIL_FROM_ADDRESS', '<EMAIL>');
define('EMAIL_FROM_NAME', 'Calaguas Booking System');
define('EMAIL_REPLY_TO', '<EMAIL>');
```

### Server Requirements

**PHP Mail Function**:
- Ensure your server has `mail()` function enabled
- Configure sendmail or postfix on your server
- Set proper SPF/DKIM records for your domain

**Alternative SMTP Setup**:
If you prefer SMTP in production, keep the original files:
- `config/email.php` (with SMTP)
- `inc.forgotpassword.php` (with SMTP)
- `inc.resetpassword.php` (with SMTP)

## File Structure After Deployment

```
/
├── forgotpassword.php              # Forgot password form
├── resetpassword.php               # Password reset form  
├── inc.forgotpassword.php          # Simple email processing
├── inc.resetpassword.php           # Simple email processing
├── config/
│   └── email.php                   # Simple email config
├── connection/
│   └── dbconnect.php              # Database connection
└── login.php                      # Updated with forgot password link
```

## Testing Production Setup

### 1. Test Database Connection
```bash
# Visit setup script to verify tables
http://yourdomain.com/create_password_reset_tables.php
```

### 2. Test Email Functionality
```bash
# Test forgot password flow
http://yourdomain.com/forgotpassword.php
```

### 3. Monitor Logs
Check your `system_logs` table for:
- Password reset requests
- Email sending attempts
- System errors

```sql
SELECT * FROM system_logs 
WHERE type IN ('Reset Request', 'Reset Complete', 'Email') 
ORDER BY date DESC 
LIMIT 20;
```

## Security Checklist

### ✅ Before Going Live

- [ ] Delete development files (`test_smtp.php`, etc.)
- [ ] Verify HTTPS is enabled
- [ ] Test password reset flow end-to-end
- [ ] Configure server mail settings
- [ ] Set up email monitoring
- [ ] Review system logs
- [ ] Test rate limiting (3 attempts per hour)
- [ ] Verify CSRF protection is working

### ✅ Server Configuration

- [ ] PHP `mail()` function enabled
- [ ] Sendmail/Postfix configured
- [ ] SPF record: `v=spf1 include:_spf.hostinger.com ~all`
- [ ] DKIM configured for your domain
- [ ] Firewall allows outbound email (port 25)

## Troubleshooting

### Email Not Sending

1. **Check PHP mail configuration**:
   ```php
   <?php
   if (function_exists('mail')) {
       echo "Mail function is available";
   } else {
       echo "Mail function is NOT available";
   }
   ?>
   ```

2. **Check server mail logs**:
   ```bash
   tail -f /var/log/mail.log
   ```

3. **Test basic mail function**:
   ```php
   <?php
   $result = mail('<EMAIL>', 'Test', 'Test message');
   echo $result ? 'Success' : 'Failed';
   ?>
   ```

### Common Issues

**Issue**: Emails go to spam
- **Solution**: Configure SPF, DKIM, and DMARC records

**Issue**: Mail function returns false
- **Solution**: Check server mail configuration

**Issue**: Long delays in email delivery
- **Solution**: Check server mail queue and configuration

**Issue**: Rate limiting not working
- **Solution**: Verify session configuration

## Monitoring & Maintenance

### Log Monitoring
```sql
-- Check recent password reset activity
SELECT type, description, date 
FROM system_logs 
WHERE type LIKE '%Reset%' 
ORDER BY date DESC;

-- Check email sending issues
SELECT * FROM system_logs 
WHERE type = 'Email' AND description LIKE '%FAILED%';
```

### Regular Maintenance
- Clean old log entries monthly
- Monitor failed email attempts
- Review security logs for suspicious activity
- Update password complexity requirements as needed

## Support

### Log Analysis
All password reset activities are logged in `system_logs`:
- `Reset Request` - Password reset initiated
- `Reset Complete` - Password successfully changed  
- `Email` - Email sending attempts

### Performance
- Average reset time: < 2 seconds
- Email delivery: Depends on server configuration
- Token expiry: 1 hour (configurable)

## Backup Strategy

### Before Deployment
The deployment script automatically creates backups:
- `inc.forgotpassword_smtp_backup.php`
- `inc.resetpassword_smtp_backup.php`
- `config/email_smtp_backup.php`

### Regular Backups
- Database: Include `system_logs` table
- Files: Backup all password reset related files
- Configuration: Backup email and database settings

## Version Information

- **Production Version**: Simple PHP mail()
- **Development Version**: SMTP with Hostinger
- **PHP Version**: 7.4+
- **Database**: MySQL 5.7+
- **Dependencies**: PDO, mail() function
