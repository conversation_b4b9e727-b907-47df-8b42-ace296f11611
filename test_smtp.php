<?php
/**
 * SMTP Email Test Script
 * 
 * This script tests the SMTP email configuration for the password reset system.
 * Run this once to verify your email settings are working correctly.
 */

// Include database connection and email configuration
require_once 'connection/dbconnect.php';
require_once 'config/email.php';

// Security check - only allow access from localhost or specific IPs
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowedIPs) && !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1'])) {
    die('Access denied. This test script can only be run locally.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SMTP Email Test - Calaguas Booking System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .console { 
            background: #1a1a1a; 
            color: #00ff00; 
            font-family: 'Courier New', monospace; 
            padding: 20px; 
            border-radius: 8px; 
            margin: 20px 0;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #00ff00; }
        .error { color: #ff4444; }
        .warning { color: #ffaa00; }
        .info { color: #4488ff; }
    </style>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto max-w-4xl px-4">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">🔧 SMTP Email Configuration Test</h1>
            
            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            <strong>Important:</strong> This test script should only be used during development and testing. 
                            Delete this file after confirming your email configuration works.
                        </p>
                    </div>
                </div>
            </div>

            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                <div class="console">
                    <div class="info">📧 SMTP Email Test Started...</div>
                    <div class="info">⚙️ Configuration:</div>
                    <div>&nbsp;&nbsp;• SMTP Host: <?= SMTP_HOST ?></div>
                    <div>&nbsp;&nbsp;• SMTP Port: <?= SMTP_PORT ?></div>
                    <div>&nbsp;&nbsp;• Username: <?= SMTP_USERNAME ?></div>
                    <div>&nbsp;&nbsp;• Encryption: <?= SMTP_ENCRYPTION ?></div>
                    <div>&nbsp;&nbsp;• From: <?= EMAIL_FROM_NAME ?> &lt;<?= EMAIL_FROM_ADDRESS ?>&gt;</div>
                    <br>

                    <?php
                    $testEmail = trim($_POST['test_email'] ?? '');
                    
                    if (empty($testEmail) || !filter_var($testEmail, FILTER_VALIDATE_EMAIL)) {
                        echo '<div class="error">❌ Invalid email address provided</div>';
                    } else {
                        echo '<div class="info">📤 Sending test email to: ' . htmlspecialchars($testEmail) . '</div>';
                        
                        // Test the SMTP connection and email sending
                        $subject = "SMTP Test - Calaguas Booking System";
                        $message = "
                        <html>
                        <head><title>SMTP Test Email</title></head>
                        <body style='font-family: Arial, sans-serif; padding: 20px;'>
                            <h2 style='color: #3b82f6;'>✅ SMTP Test Successful!</h2>
                            <p>This is a test email from the Calaguas Booking System.</p>
                            <p><strong>Test Details:</strong></p>
                            <ul>
                                <li>SMTP Host: " . SMTP_HOST . "</li>
                                <li>SMTP Port: " . SMTP_PORT . "</li>
                                <li>Sent at: " . date('Y-m-d H:i:s') . "</li>
                            </ul>
                            <p>If you received this email, your SMTP configuration is working correctly!</p>
                            <hr>
                            <p style='font-size: 12px; color: #666;'>
                                This is an automated test message from the Calaguas Booking System password reset functionality.
                            </p>
                        </body>
                        </html>
                        ";
                        
                        $startTime = microtime(true);
                        $result = sendSMTPEmail($testEmail, $subject, $message);
                        $endTime = microtime(true);
                        $duration = round(($endTime - $startTime) * 1000, 2);
                        
                        if ($result) {
                            echo '<div class="success">✅ Email sent successfully!</div>';
                            echo '<div class="success">⏱️ Send time: ' . $duration . 'ms</div>';
                            echo '<div class="info">📬 Please check your inbox (and spam folder) for the test email</div>';
                            
                            // Log the test
                            try {
                                $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (:type, :description)");
                                $stmt->execute([
                                    ':type' => 'SMTP Test',
                                    ':description' => 'SMTP test email sent to: ' . $testEmail
                                ]);
                                echo '<div class="info">📝 Test logged to system_logs table</div>';
                            } catch (Exception $e) {
                                echo '<div class="warning">⚠️ Could not log test: ' . htmlspecialchars($e->getMessage()) . '</div>';
                            }
                        } else {
                            echo '<div class="error">❌ Email sending failed!</div>';
                            echo '<div class="error">⏱️ Failed after: ' . $duration . 'ms</div>';
                            echo '<div class="warning">🔍 Check PHP error logs for detailed error messages</div>';
                            echo '<div class="info">💡 Common issues:</div>';
                            echo '<div>&nbsp;&nbsp;• Incorrect SMTP credentials</div>';
                            echo '<div>&nbsp;&nbsp;• Firewall blocking outbound connections</div>';
                            echo '<div>&nbsp;&nbsp;• SSL/TLS certificate issues</div>';
                            echo '<div>&nbsp;&nbsp;• Server IP not whitelisted by email provider</div>';
                        }
                    }
                    ?>
                    
                    <br>
                    <div class="info">🏁 Test completed</div>
                </div>
            <?php endif; ?>

            <form method="POST" class="space-y-4">
                <div>
                    <label for="test_email" class="block text-sm font-medium text-gray-700 mb-2">
                        📧 Test Email Address
                    </label>
                    <input 
                        type="email" 
                        id="test_email" 
                        name="test_email" 
                        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter your email address to receive a test email"
                        required
                        value="<?= htmlspecialchars($_POST['test_email'] ?? '', ENT_QUOTES, 'UTF-8') ?>"
                    >
                    <p class="mt-1 text-sm text-gray-500">
                        We'll send a test email to this address to verify SMTP configuration
                    </p>
                </div>

                <button 
                    type="submit" 
                    class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                >
                    🚀 Send Test Email
                </button>
            </form>

            <div class="mt-8 bg-gray-50 rounded-lg p-4">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">📋 Current SMTP Configuration</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                        <strong>SMTP Host:</strong> <?= SMTP_HOST ?>
                    </div>
                    <div>
                        <strong>SMTP Port:</strong> <?= SMTP_PORT ?>
                    </div>
                    <div>
                        <strong>Username:</strong> <?= SMTP_USERNAME ?>
                    </div>
                    <div>
                        <strong>Encryption:</strong> <?= SMTP_ENCRYPTION ?>
                    </div>
                    <div>
                        <strong>From Address:</strong> <?= EMAIL_FROM_ADDRESS ?>
                    </div>
                    <div>
                        <strong>From Name:</strong> <?= EMAIL_FROM_NAME ?>
                    </div>
                </div>
            </div>

            <div class="mt-6 bg-yellow-50 border-l-4 border-yellow-400 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-yellow-700">
                            <strong>Security Notice:</strong> This test script contains sensitive SMTP credentials. 
                            Delete this file (<code>test_smtp.php</code>) after confirming your email configuration works properly.
                        </p>
                    </div>
                </div>
            </div>

            <div class="mt-6 text-center">
                <a href="forgotpassword.php" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    🔑 Test Forgot Password System
                </a>
                <a href="login.php" class="ml-4 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    🏠 Back to Login
                </a>
            </div>
        </div>
    </div>
</body>
</html>
