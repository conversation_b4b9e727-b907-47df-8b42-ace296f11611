<?php
/**
 * Environment Configuration Loader
 * 
 * This file loads environment variables from .env file
 * and provides helper functions for accessing configuration values.
 */

class EnvLoader {
    private static $loaded = false;
    private static $env = [];

    /**
     * Load environment variables from .env file
     */
    public static function load($envFile = null) {
        if (self::$loaded) {
            return;
        }

        if ($envFile === null) {
            $envFile = __DIR__ . '/../.env';
        }

        if (!file_exists($envFile)) {
            // Try to find .env file in parent directories
            $currentDir = dirname($envFile);
            for ($i = 0; $i < 3; $i++) {
                $testFile = $currentDir . '/.env';
                if (file_exists($testFile)) {
                    $envFile = $testFile;
                    break;
                }
                $currentDir = dirname($currentDir);
            }
        }

        if (file_exists($envFile)) {
            $lines = file($envFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            
            foreach ($lines as $line) {
                // Skip comments
                if (strpos(trim($line), '#') === 0) {
                    continue;
                }

                // Parse key=value pairs
                if (strpos($line, '=') !== false) {
                    list($key, $value) = explode('=', $line, 2);
                    $key = trim($key);
                    $value = trim($value);

                    // Remove quotes if present
                    if ((substr($value, 0, 1) === '"' && substr($value, -1) === '"') ||
                        (substr($value, 0, 1) === "'" && substr($value, -1) === "'")) {
                        $value = substr($value, 1, -1);
                    }

                    // Store in both $_ENV and our internal array
                    $_ENV[$key] = $value;
                    self::$env[$key] = $value;
                    
                    // Also set as environment variable if not already set
                    if (!getenv($key)) {
                        putenv("$key=$value");
                    }
                }
            }
        }

        self::$loaded = true;
    }

    /**
     * Get environment variable value
     * 
     * @param string $key Environment variable key
     * @param mixed $default Default value if key not found
     * @return mixed
     */
    public static function get($key, $default = null) {
        self::load();

        // Check $_ENV first
        if (isset($_ENV[$key])) {
            return self::parseValue($_ENV[$key]);
        }

        // Check getenv()
        $value = getenv($key);
        if ($value !== false) {
            return self::parseValue($value);
        }

        // Check our internal array
        if (isset(self::$env[$key])) {
            return self::parseValue(self::$env[$key]);
        }

        return $default;
    }

    /**
     * Parse environment value to appropriate type
     * 
     * @param string $value
     * @return mixed
     */
    private static function parseValue($value) {
        // Handle boolean values
        if (strtolower($value) === 'true') {
            return true;
        }
        if (strtolower($value) === 'false') {
            return false;
        }

        // Handle null values
        if (strtolower($value) === 'null') {
            return null;
        }

        // Handle numeric values
        if (is_numeric($value)) {
            return strpos($value, '.') !== false ? (float)$value : (int)$value;
        }

        return $value;
    }

    /**
     * Check if environment variable exists
     * 
     * @param string $key
     * @return bool
     */
    public static function has($key) {
        self::load();
        return isset($_ENV[$key]) || getenv($key) !== false || isset(self::$env[$key]);
    }

    /**
     * Get all environment variables
     * 
     * @return array
     */
    public static function all() {
        self::load();
        return array_merge(self::$env, $_ENV);
    }
}

// Helper function for easy access
if (!function_exists('env')) {
    /**
     * Get environment variable value
     * 
     * @param string $key
     * @param mixed $default
     * @return mixed
     */
    function env($key, $default = null) {
        return EnvLoader::get($key, $default);
    }
}

// Auto-load environment variables
EnvLoader::load();
?>
