<?php
function env($key, $default = null) {
    static $env = null;
    if ($env === null) {
        $env = [];
        if (file_exists(__DIR__ . "/../.env")) {
            $lines = file(__DIR__ . "/../.env", FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, "=") !== false && strpos($line, "#") !== 0) {
                    list($key, $value) = explode("=", $line, 2);
                    $env[trim($key)] = trim($value);
                }
            }
        }
    }
    return isset($env[$key]) ? $env[$key] : $default;
}
?>