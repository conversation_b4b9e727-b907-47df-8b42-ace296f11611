<?php
/**
 * Simple Environment Setup
 * This script will ONLY modify your existing files to use .env
 */

// Security check
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowedIPs)) {
    die('Access denied. Run locally only.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple .env Setup</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto max-w-2xl px-4">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">🎯 Simple .env Setup</h1>
            
            <div class="bg-green-50 border-l-4 border-green-400 p-4 mb-6">
                <p class="text-sm text-green-700">
                    <strong>Simple Approach:</strong> This will only modify 3 existing files to use .env configuration.
                    No extra files, no confusion!
                </p>
            </div>

            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST'): ?>
                <div class="bg-gray-900 text-green-400 p-4 rounded-lg mb-6 font-mono text-sm">
                    <?php
                    try {
                        // Step 1: Create .env file
                        echo "📝 Creating .env file...\n";
                        $envContent = 'DB_HOST=localhost
DB_PORT=3306
DB_NAME=prod_calaguas
DB_USER=root
DB_PASSWORD=
EMAIL_FROM_ADDRESS=<EMAIL>
EMAIL_FROM_NAME=Calaguas Booking System
SMTP_HOST=smtp.hostinger.com
SMTP_PORT=465
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=4>v4s@TWg~K
APP_DEBUG=false';
                        
                        file_put_contents('.env', $envContent);
                        echo "✅ .env file created\n";

                        // Step 2: Create simple env loader
                        echo "📝 Creating config/env.php...\n";
                        $envLoader = '<?php
function env($key, $default = null) {
    static $env = null;
    if ($env === null) {
        $env = [];
        if (file_exists(__DIR__ . "/../.env")) {
            $lines = file(__DIR__ . "/../.env", FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            foreach ($lines as $line) {
                if (strpos($line, "=") !== false && strpos($line, "#") !== 0) {
                    list($key, $value) = explode("=", $line, 2);
                    $env[trim($key)] = trim($value);
                }
            }
        }
    }
    return isset($env[$key]) ? $env[$key] : $default;
}
?>';
                        file_put_contents('config/env.php', $envLoader);
                        echo "✅ Environment loader created\n";

                        // Step 3: Update dbconnect.php
                        echo "📝 Updating connection/dbconnect.php...\n";
                        $dbContent = '<?php
require_once __DIR__ . "/../config/env.php";

$host = env("DB_HOST", "localhost");
$port = env("DB_PORT", "3306");
$dbname = env("DB_NAME", "prod_calaguas");
$username = env("DB_USER", "root");
$password = env("DB_PASSWORD", "");

try {
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=utf8mb4";
    $options = [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,
        PDO::ATTR_TIMEOUT => 5,
        PDO::MYSQL_ATTR_FOUND_ROWS => true,
        PDO::ATTR_PERSISTENT => false,
    ];
    
    $pdo = new PDO($dsn, $username, $password, $options);
    unset($host, $port, $username, $password, $dsn);
    
} catch (PDOException $e) {
    error_log("Database connection failed: " . $e->getMessage());
    if (env("APP_DEBUG", false)) {
        die("Database connection failed: " . $e->getMessage());
    } else {
        die("Database connection failed. Please try again later.");
    }
}
?>';
                        file_put_contents('connection/dbconnect.php', $dbContent);
                        echo "✅ Database connection updated\n";

                        // Step 4: Update email config
                        echo "📝 Updating config/email.php...\n";
                        $emailContent = '<?php
require_once __DIR__ . "/env.php";

define("EMAIL_FROM_ADDRESS", env("EMAIL_FROM_ADDRESS", "<EMAIL>"));
define("EMAIL_FROM_NAME", env("EMAIL_FROM_NAME", "Calaguas Booking System"));

function sendPasswordResetEmail($to, $username, $resetLink) {
    $subject = "Password Reset Request - " . EMAIL_FROM_NAME;
    $message = getPasswordResetEmailTemplate($username, $resetLink);
    $headers = getEmailHeaders();
    return mail($to, $subject, $message, $headers);
}

function sendPasswordResetConfirmationEmail($to, $username) {
    $subject = "Password Reset Successful - " . EMAIL_FROM_NAME;
    $message = getPasswordResetConfirmationTemplate($username);
    $headers = getEmailHeaders();
    return mail($to, $subject, $message, $headers);
}

function getEmailHeaders() {
    $headers = "MIME-Version: 1.0\r\n";
    $headers .= "Content-type:text/html;charset=UTF-8\r\n";
    $headers .= "From: " . EMAIL_FROM_NAME . " <" . EMAIL_FROM_ADDRESS . ">\r\n";
    $headers .= "Reply-To: " . EMAIL_FROM_ADDRESS . "\r\n";
    return $headers;
}

function getPasswordResetEmailTemplate($username, $resetLink) {
    return "
    <html>
    <body style=\"font-family: Arial, sans-serif; padding: 20px;\">
        <h2 style=\"color: #3b82f6;\">Password Reset Request</h2>
        <p>Hello <strong>" . htmlspecialchars($username) . "</strong>,</p>
        <p>Click the link below to reset your password:</p>
        <p><a href=\"" . $resetLink . "\" style=\"background: #3b82f6; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;\">Reset Password</a></p>
        <p>This link expires in 1 hour.</p>
        <p>If you did not request this, please ignore this email.</p>
    </body>
    </html>";
}

function getPasswordResetConfirmationTemplate($username) {
    return "
    <html>
    <body style=\"font-family: Arial, sans-serif; padding: 20px;\">
        <h2 style=\"color: #10b981;\">Password Reset Successful</h2>
        <p>Hello <strong>" . htmlspecialchars($username) . "</strong>,</p>
        <p>Your password has been successfully reset.</p>
        <p>You can now log in with your new password.</p>
    </body>
    </html>";
}

function logEmailAttempt($type, $to, $success, $error = "") {
    global $pdo;
    try {
        $description = "Email: $type to $to";
        if (!$success) $description .= " - FAILED";
        if (strlen($description) > 100) $description = substr($description, 0, 97) . "...";
        
        $stmt = $pdo->prepare("INSERT INTO system_logs (type, description) VALUES (?, ?)");
        $stmt->execute(["Email", $description]);
    } catch (Exception $e) {
        error_log("Failed to log email: " . $e->getMessage());
    }
}
?>';
                        file_put_contents('config/email.php', $emailContent);
                        echo "✅ Email configuration updated\n";

                        echo "\n🎉 Setup complete! Only 3 files modified:\n";
                        echo "   • connection/dbconnect.php\n";
                        echo "   • config/email.php\n";
                        echo "   • config/env.php (new)\n";
                        echo "   • .env (new)\n";

                    } catch (Exception $e) {
                        echo "❌ Error: " . $e->getMessage();
                    }
                    ?>
                </div>
                
                <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                    <h3 class="text-sm font-medium text-green-800">✅ Setup Complete!</h3>
                    <p class="mt-2 text-sm text-green-700">
                        Your system now uses .env for configuration. Update the .env file with your actual database password.
                    </p>
                </div>
                
                <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                    <h3 class="text-sm font-medium text-yellow-800">📝 Next Steps:</h3>
                    <ol class="mt-2 text-sm text-yellow-700 list-decimal list-inside">
                        <li>Edit .env file with your database password</li>
                        <li>Test the forgot password system</li>
                        <li>Delete this setup file (simple_env_setup.php)</li>
                    </ol>
                </div>
                
            <?php else: ?>
                <div class="space-y-4">
                    <p class="text-gray-700">This will:</p>
                    <ul class="list-disc list-inside space-y-1 text-gray-600 ml-4">
                        <li>Create a simple .env file</li>
                        <li>Update connection/dbconnect.php to use .env</li>
                        <li>Update config/email.php to use .env</li>
                        <li>Create a simple config/env.php loader</li>
                    </ul>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <p class="text-sm text-blue-700">
                            <strong>Simple & Clean:</strong> No extra files, no confusion. Just clean environment configuration.
                        </p>
                    </div>
                    
                    <form method="POST" class="text-center">
                        <button 
                            type="submit" 
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700"
                        >
                            🎯 Setup Simple .env Configuration
                        </button>
                    </form>
                </div>
            <?php endif; ?>
            
            <div class="mt-6 text-center">
                <a href="forgotpassword.php" class="text-blue-600 hover:underline">Test Forgot Password</a>
                <span class="mx-2">|</span>
                <a href="login.php" class="text-blue-600 hover:underline">Back to Login</a>
            </div>
        </div>
    </div>
</body>
</html>
