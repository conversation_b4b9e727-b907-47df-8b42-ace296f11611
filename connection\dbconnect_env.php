<?php
/**
 * Environment-based Database Connection
 *
 * This file handles the database connection using environment variables
 * from .env file for better security and easier deployment.
 */

// Load environment configuration
require_once __DIR__ . '/../config/env.php';

// Get database configuration from environment variables
$host = env('DB_HOST', 'localhost');
$port = env('DB_PORT', '3306');
$dbname = env('DB_NAME', 'prod_calaguas');
$username = env('DB_USER', 'root');
$password = env('DB_PASSWORD', '');
$charset = env('DB_CHARSET', 'utf8mb4');

// Validate required configuration
if (empty($dbname)) {
    error_log("Database configuration error: DB_NAME is required");
    die("Database configuration error. Please check your .env file.");
}

// Connect to database with enhanced security options
try {
    $dsn = "mysql:host=$host;port=$port;dbname=$dbname;charset=$charset";
    $options = [
        // Error handling
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,

        // Security enhancements
        PDO::ATTR_TIMEOUT => 5, // Connection timeout in seconds
        PDO::MYSQL_ATTR_FOUND_ROWS => true,
        PDO::ATTR_PERSISTENT => false, // Disable persistent connections
    ];

    // Enable SSL/TLS if available and configured
    $sslCa = env('DB_SSL_CA');
    if ($sslCa && file_exists($sslCa)) {
        $options[PDO::MYSQL_ATTR_SSL_CA] = $sslCa;
        $options[PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT] = env('DB_SSL_VERIFY', true);
    }

    $pdo = new PDO($dsn, $username, $password, $options);

    // Clear sensitive variables
    unset($host, $port, $username, $password, $dsn);

} catch (PDOException $e) {
    // Log detailed error but show generic message to users
    error_log("Database connection failed: " . $e->getMessage());
    
    // Show more detailed error in development
    if (env('APP_DEBUG', false)) {
        die("Database connection failed: " . $e->getMessage());
    } else {
        die("Database connection failed. Please try again later.");
    }
}

// Set timezone if configured
$timezone = env('DB_TIMEZONE');
if ($timezone) {
    try {
        $pdo->exec("SET time_zone = '$timezone'");
    } catch (PDOException $e) {
        error_log("Failed to set database timezone: " . $e->getMessage());
    }
}
?>
