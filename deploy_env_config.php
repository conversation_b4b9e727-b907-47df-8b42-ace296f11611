<?php
/**
 * Environment Configuration Deployment Script
 * 
 * This script switches the system to use environment variables (.env file)
 * for configuration instead of the encrypted config files.
 */

// Security check - only allow access from localhost
$allowedIPs = ['127.0.0.1', '::1', 'localhost'];
if (!in_array($_SERVER['REMOTE_ADDR'], $allowedIPs) && !in_array($_SERVER['HTTP_HOST'], ['localhost', '127.0.0.1'])) {
    die('Access denied. This deployment script can only be run locally.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Deploy Environment Configuration</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 min-h-screen py-8">
    <div class="container mx-auto max-w-4xl px-4">
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h1 class="text-2xl font-bold text-gray-800 mb-6">🌍 Deploy Environment Configuration</h1>
            
            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 mb-6">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <p class="text-sm text-blue-700">
                            <strong>Purpose:</strong> This script switches the system to use .env file for configuration, 
                            making deployment easier and more secure.
                        </p>
                    </div>
                </div>
            </div>

            <?php if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['deploy'])): ?>
                <div class="bg-gray-900 text-green-400 p-4 rounded-lg mb-6 font-mono text-sm">
                    <div class="mb-2">🔄 Starting environment configuration deployment...</div>
                    
                    <?php
                    $success = true;
                    $errors = [];
                    
                    try {
                        // Step 1: Check if .env file exists
                        echo "<div>📋 Checking .env file...</div>";
                        
                        if (!file_exists('.env')) {
                            if (file_exists('.env.example')) {
                                if (copy('.env.example', '.env')) {
                                    echo "<div>✅ Created .env from .env.example</div>";
                                    echo "<div class='text-yellow-400'>⚠️ Please update .env with your actual configuration</div>";
                                } else {
                                    throw new Exception("Failed to create .env file from .env.example");
                                }
                            } else {
                                throw new Exception(".env file not found and .env.example not available");
                            }
                        } else {
                            echo "<div>✅ .env file exists</div>";
                        }
                        
                        // Step 2: Backup current files
                        echo "<div class='mt-2'>📦 Creating backups...</div>";
                        
                        $filesToBackup = [
                            'connection/dbconnect.php' => 'connection/dbconnect_encrypted_backup.php',
                            'config/config.php' => 'config/config_encrypted_backup.php',
                            'inc.forgotpassword.php' => 'inc.forgotpassword_encrypted_backup.php',
                            'inc.resetpassword.php' => 'inc.resetpassword_encrypted_backup.php'
                        ];
                        
                        foreach ($filesToBackup as $source => $backup) {
                            if (file_exists($source)) {
                                if (copy($source, $backup)) {
                                    echo "<div>✅ Backed up $source</div>";
                                } else {
                                    throw new Exception("Failed to backup $source");
                                }
                            }
                        }
                        
                        // Step 3: Deploy environment-based files
                        echo "<div class='mt-2'>🔄 Deploying environment configuration...</div>";
                        
                        $filesToDeploy = [
                            'connection/dbconnect_env.php' => 'connection/dbconnect.php',
                            'config/email_env.php' => 'config/email.php',
                            'inc.forgotpassword_env.php' => 'inc.forgotpassword.php',
                            'inc.resetpassword_env.php' => 'inc.resetpassword.php'
                        ];
                        
                        foreach ($filesToDeploy as $source => $target) {
                            if (file_exists($source)) {
                                if (copy($source, $target)) {
                                    echo "<div>✅ Deployed $target</div>";
                                } else {
                                    throw new Exception("Failed to deploy $target");
                                }
                            } else {
                                echo "<div class='text-yellow-400'>⚠️ Source file $source not found</div>";
                            }
                        }
                        
                        // Step 4: Test environment loading
                        echo "<div class='mt-2'>🧪 Testing environment configuration...</div>";
                        
                        try {
                            require_once 'config/env.php';
                            $testValue = env('DB_HOST', 'test_default');
                            echo "<div>✅ Environment loader working (DB_HOST: $testValue)</div>";
                        } catch (Exception $e) {
                            echo "<div class='text-yellow-400'>⚠️ Environment test warning: " . htmlspecialchars($e->getMessage()) . "</div>";
                        }
                        
                        echo "<div class='mt-2 text-green-300'>✅ Environment configuration deployment completed!</div>";
                        
                    } catch (Exception $e) {
                        $success = false;
                        $errors[] = $e->getMessage();
                        echo "<div class='text-red-400'>❌ Error: " . htmlspecialchars($e->getMessage()) . "</div>";
                    }
                    ?>
                </div>
                
                <?php if ($success): ?>
                    <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-green-800">Deployment Successful!</h3>
                                <div class="mt-2 text-sm text-green-700">
                                    <p>The system has been successfully configured to use environment variables.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">Important Next Steps</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <ol class="list-decimal list-inside space-y-1">
                                        <li>Update your <code>.env</code> file with correct database credentials</li>
                                        <li>Set <code>APP_ENV=production</code> and <code>APP_DEBUG=false</code> for production</li>
                                        <li>Configure your email settings in <code>.env</code></li>
                                        <li>Test the forgot password functionality</li>
                                        <li>Add <code>.env</code> to your <code>.gitignore</code> file</li>
                                        <li>Delete this deployment script for security</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-800">Deployment Failed</h3>
                                <div class="mt-2 text-sm text-red-700">
                                    <p>The deployment encountered errors. Please check the console output above and try again.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="space-y-6">
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">🌍 Environment Configuration Benefits:</h3>
                        <ul class="list-disc list-inside space-y-2 text-gray-700">
                            <li>✅ <strong>Security:</strong> Sensitive data stored in .env file (not in code)</li>
                            <li>✅ <strong>Flexibility:</strong> Easy to change settings without code changes</li>
                            <li>✅ <strong>Deployment:</strong> Different settings for dev/staging/production</li>
                            <li>✅ <strong>Version Control:</strong> .env file excluded from git</li>
                            <li>✅ <strong>Simplicity:</strong> No encryption/decryption complexity</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">📁 What this deployment does:</h3>
                        <ul class="list-disc list-inside space-y-2 text-gray-700">
                            <li>Creates .env file from .env.example if needed</li>
                            <li>Backs up current encrypted configuration files</li>
                            <li>Replaces them with environment-based versions</li>
                            <li>Tests the new configuration system</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-lg font-medium text-gray-900 mb-3">📋 Files that will be modified:</h3>
                        <div class="bg-gray-50 rounded-lg p-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                <div>
                                    <strong>Replaced:</strong>
                                    <ul class="mt-1 space-y-1 text-gray-600">
                                        <li>• connection/dbconnect.php</li>
                                        <li>• config/email.php</li>
                                        <li>• inc.forgotpassword.php</li>
                                        <li>• inc.resetpassword.php</li>
                                    </ul>
                                </div>
                                <div>
                                    <strong>Created:</strong>
                                    <ul class="mt-1 space-y-1 text-gray-600">
                                        <li>• .env (if not exists)</li>
                                        <li>• config/env.php</li>
                                        <li>• Backup files (*_backup.php)</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-blue-50 border border-blue-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-blue-800">💡 Recommendation</h3>
                                <div class="mt-2 text-sm text-blue-700">
                                    <p>Environment-based configuration is the modern standard for web applications. 
                                    It's more secure, flexible, and easier to manage across different environments.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <form method="POST" class="text-center">
                        <button 
                            type="submit" 
                            name="deploy"
                            value="1"
                            class="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-200"
                            onclick="return confirm('Are you sure you want to deploy environment-based configuration? This will modify your files.')"
                        >
                            🌍 Deploy Environment Configuration
                        </button>
                    </form>
                </div>
            <?php endif; ?>
            
            <div class="mt-8 text-center">
                <a href="forgotpassword.php" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-blue-600 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    🔑 Test Forgot Password
                </a>
                <a href="login.php" class="ml-4 inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    🏠 Back to Login
                </a>
            </div>
        </div>
    </div>
</body>
</html>
