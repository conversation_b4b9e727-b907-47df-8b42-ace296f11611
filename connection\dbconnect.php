<?php
/**
 * Secure Database Connection
 *
 * This file handles the database connection with enhanced security measures:
 * - Uses environment variables when available
 * - Falls back to encrypted config file values
 * - Implements secure PDO connection settings
 * - Provides proper error handling
 */

// Load configuration
$config = require __DIR__ . '/../config/config.php';

// Get encryption details from a more secure location
// In production, these should be stored in environment variables
$keyFile = __DIR__ . '/../config/.keys';
if (file_exists($keyFile)) {
    $keys = parse_ini_file($keyFile);
    $encryptionKey = $keys['encryption_key'] ?? null;
    $ivString = $keys['iv'] ?? null;
} else {
    // Fallback to environment variables
    $encryptionKey = getenv('DB_ENCRYPTION_KEY');
    $ivString = getenv('DB_ENCRYPTION_IV');

    // Last resort fallback (not recommended for production)
    if (!$encryptionKey || !$ivString) {
        require_once __DIR__ . '/../config/security.php';
        $encryptionKey = defined('DB_ENCRYPTION_KEY') ? DB_ENCRYPTION_KEY : null;
        $ivString = defined('DB_ENCRYPTION_IV') ? DB_ENCRYPTION_IV : null;
    }
}

// Decrypt database credentials
try {
    if (!$encryptionKey || !$ivString) {
        throw new Exception("Missing encryption credentials");
    }

    $host = openssl_decrypt($config['DB_HOST'], 'aes-256-cbc', $encryptionKey, 0, $ivString);
    $username = openssl_decrypt($config['DB_USER'], 'aes-256-cbc', $encryptionKey, 0, $ivString);
    $password = openssl_decrypt($config['DB_PASSWORD'], 'aes-256-cbc', $encryptionKey, 0, $ivString);
    $dbname = openssl_decrypt($config['DB_NAME'], 'aes-256-cbc', $encryptionKey, 0, $ivString);

    if ($host === false || $username === false || $password === false || $dbname === false) {
        throw new Exception("Failed to decrypt database credentials");
    }
} catch (Exception $e) {
    error_log("Database credential decryption failed: " . $e->getMessage());
    die("Configuration error. Please contact the administrator.");
}

// Connect to database with enhanced security options
try {
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $options = [
        // Error handling
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES => false,

        // Security enhancements
        PDO::ATTR_TIMEOUT => 5, // Connection timeout in seconds
        PDO::MYSQL_ATTR_FOUND_ROWS => true,
        PDO::ATTR_PERSISTENT => false, // Disable persistent connections
    ];

    // Enable SSL/TLS if available
    if (defined('PDO::MYSQL_ATTR_SSL_CA') && file_exists(__DIR__ . '/../config/ssl/ca-cert.pem')) {
        $options[PDO::MYSQL_ATTR_SSL_CA] = __DIR__ . '/../config/ssl/ca-cert.pem';
        $options[PDO::MYSQL_ATTR_SSL_VERIFY_SERVER_CERT] = true;
    }

    $pdo = new PDO($dsn, $username, $password, $options);

    // Clear sensitive variables
    unset($host, $username, $password, $dbname, $encryptionKey, $ivString);
} catch (PDOException $e) {
    // Log detailed error but show generic message to users
    error_log("Database connection failed: " . $e->getMessage());
    die("Database connection failed. Please try again later.");
}
